using Orleans;
using Curio.Queries.Interfaces;
using Curio.Queries.Converters;
using Curio.Orleans.Interfaces.Users;
using Curio.Shared.Enums;
using Microsoft.Extensions.Logging;
using Curio.Shared.Users;

namespace Curio.Queries.Handlers;

/// <summary>
/// 用户查询处理器 - 符合CQRS规范的查询端
/// 实现标准化的查询处理接口，未来可从投影查询以提高性能
/// </summary>
public class UserQueryHandler :
    IQueryHandler<string, UserDto?>,
    IQueryHandler<CheckEmailExistsCommand, EmailExistsResult>
{
    private readonly IGrainFactory _grainFactory;
    private readonly ILogger<UserQueryHandler> _logger;

    public UserQueryHandler(IGrainFactory grainFactory, ILogger<UserQueryHandler> logger)
    {
        _grainFactory = grainFactory;
        _logger = logger;
    }

    /// <summary>
    /// 查询用户信息（以邮箱为查询参数）
    /// </summary>
    public async Task<UserDto?> HandleAsync(string email)
    {
        var userGrain = _grainFactory.GetGrain<IUserGrain>(email);
        return await userGrain.GetUserAsync();
    }

    /// <summary>
    /// 检查邮箱是否存在
    /// </summary>
    public async Task<EmailExistsResult> HandleAsync(CheckEmailExistsCommand query)
    {
        if (query == null || string.IsNullOrWhiteSpace(query.Email))
        {
            _logger.LogWarning("CheckEmailExistsCommand is null or email is empty");
            return new EmailExistsResult
            {
                Exists = false,
                IsVerified = false
            };
        }

        try
        {
            // 转换为领域查询并验证
            var domainQuery = query.ToDomainCommand("web-api-query");
            domainQuery.ValidateQuery();

            // 调用新的领域方法
            var userGrain = _grainFactory.GetGrain<IUserGrain>(domainQuery.Email);
            return await userGrain.CheckUserExistsAsync(domainQuery);
        }
        catch (ArgumentException ex)
        {
            _logger.LogWarning("Invalid query for email check: {Message}", ex.Message);
            return new EmailExistsResult
            {
                Exists = false,
                IsVerified = false
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email existence for {Email}", query.Email);
            return new EmailExistsResult
            {
                Exists = false,
                IsVerified = false
            };
        }
    }

    // 向后兼容方法（逐步移除）
    public Task<UserDto?> HandleGetUserAsync(string email) => HandleAsync(email);
    public Task<EmailExistsResult> HandleCheckEmailExistsAsync(CheckEmailExistsCommand query) => HandleAsync(query);

    // TODO: 未来添加基于投影的查询方法
    // 例如：从 PostgreSQL 读模型查询，而不是从事件流重建状态
}
