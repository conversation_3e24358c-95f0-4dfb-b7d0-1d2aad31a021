using Microsoft.AspNetCore.Mvc;
using Curio.Commands.Handlers;
using Curio.Queries.Handlers;
using Curio.Shared.Admins;
using Curio.Api.Models;
using Curio.Api.Constants;

namespace Curio.Api.Controllers;

[ApiController]
[Route("api/admin")]
internal class AdminController : BaseController
{
    private readonly AdminCommandHandler _commandHandler;
    private readonly AdminQueryHandler _queryHandler;
    private readonly ILogger<AdminController> _logger;

    public AdminController(AdminCommandHandler commandHandler, AdminQueryHandler queryHandler, ILogger<AdminController> logger)
    {
        _commandHandler = commandHandler;
        _queryHandler = queryHandler;
        _logger = logger;
    }

    /// <summary>
    /// 管理员登录
    /// </summary>
    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<LoginResult>>> Login([FromBody] AdminLoginCommand command)
    {
        try
        {
            // 从请求中获取IP地址和User-Agent
            command.IpAddress = HttpContext.Connection.RemoteIpAddress?.ToString() ?? "";
            command.UserAgent = HttpContext.Request.Headers.UserAgent.ToString();

            var result = await _commandHandler.HandleLoginAsync(command);

            if (result.Success)
            {
                return Success(result, "Login successful");
            }

            if (result.RequiresTwoFactor)
            {
                return Success(result, "Two-factor authentication required");
            }

            return BusinessError<LoginResult>(result.Message ?? "Login failed", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during admin login: {Username}", command.Username);
            return InternalError<LoginResult>("Login failed due to internal error");
        }
    }

    /// <summary>
    /// 验证两步验证码
    /// </summary>
    [HttpPost("verify-2fa")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> VerifyTwoFactor([FromBody] VerifyTwoFactorCommand command)
    {
        try
        {
            var result = await _commandHandler.HandleVerifyTwoFactorAsync(command);

            if (result.Success)
            {
                return Success(result, "Two-factor code verified successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Verification failed", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error verifying 2FA: {AdminId}", command.AdminId);
            return InternalError<AdminOperationResult>("Failed to verify two-factor code");
        }
    }

    /// <summary>
    /// 获取管理员信息
    /// </summary>
    [HttpGet("{adminId}")]
    public async Task<ActionResult<ApiResponse<AdminDto>>> GetAdmin(string adminId)
    {
        try
        {
            var admin = await _queryHandler.HandleGetAdminAsync(adminId);

            if (admin == null)
            {
                return NotFoundError<AdminDto>($"Admin with ID '{adminId}' not found");
            }

            return Success(admin, "Admin retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin: {AdminId}", adminId);
            return InternalError<AdminDto>("Failed to retrieve admin");
        }
    }

    /// <summary>
    /// 创建管理员
    /// </summary>
    [HttpPost]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> CreateAdmin([FromBody] CreateAdminCommand command)
    {
        try
        {
            // 从当前用户上下文获取创建者ID
            // TODO: 集成JWT认证后，从Token中获取当前用户ID
            command.CreatedBy = "system"; // 临时值

            var result = await _commandHandler.HandleCreateAdminAsync(command);

            if (result.Success)
            {
                return Created(result, "Admin created successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to create admin", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating admin: {Username}", command.Username);
            return InternalError<AdminOperationResult>("Failed to create admin");
        }
    }

    /// <summary>
    /// 更新管理员信息
    /// </summary>
    [HttpPut("{adminId}")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> UpdateAdmin(string adminId, [FromBody] UpdateAdminCommand command)
    {
        try
        {
            command.AdminId = adminId;
            command.UpdatedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleUpdateAdminAsync(command);

            if (result.Success)
            {
                return Success(result, "Admin updated successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to update admin", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating admin: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to update admin");
        }
    }

    /// <summary>
    /// 修改管理员密码
    /// </summary>
    [HttpPost("{adminId}/change-password")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> ChangePassword(string adminId, [FromBody] ChangeAdminPasswordCommand command)
    {
        try
        {
            command.AdminId = adminId;

            var result = await _commandHandler.HandleChangePasswordAsync(command);

            if (result.Success)
            {
                return Success(result, "Password changed successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to change password", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing password: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to change password");
        }
    }

    /// <summary>
    /// 重置管理员密码
    /// </summary>
    [HttpPost("{adminId}/reset-password")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> ResetPassword(string adminId, [FromBody] ResetPasswordRequest request)
    {
        try
        {
            var operatorId = "system"; // TODO: 从Token中获取
            var result = await _commandHandler.HandleResetPasswordAsync(adminId, request.NewPassword, operatorId);

            if (result.Success)
            {
                return Success(result, "Password reset successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to reset password", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resetting password: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to reset password");
        }
    }

    /// <summary>
    /// 解锁管理员账户
    /// </summary>
    [HttpPost("{adminId}/unlock")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> UnlockAccount(string adminId)
    {
        try
        {
            var operatorId = "system"; // TODO: 从Token中获取
            var result = await _commandHandler.HandleUnlockAccountAsync(adminId, operatorId);

            if (result.Success)
            {
                return Success(result, "Account unlocked successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to unlock account", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error unlocking account: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to unlock account");
        }
    }

    /// <summary>
    /// 更新管理员状态
    /// </summary>
    [HttpPut("{adminId}/status")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> UpdateAdminStatus(string adminId, [FromBody] UpdateAdminStatusCommand command)
    {
        try
        {
            command.AdminId = adminId;
            command.UpdatedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleUpdateStatusAsync(command);

            if (result.Success)
            {
                return Success(result, "Admin status updated successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to update admin status", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating admin status: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to update admin status");
        }
    }

    /// <summary>
    /// 分配角色给管理员
    /// </summary>
    [HttpPost("{adminId}/roles")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> AssignRole(string adminId, [FromBody] AssignRoleCommand command)
    {
        try
        {
            command.AdminId = adminId;
            command.AssignedBy = "system"; // TODO: 从Token中获取

            var result = await _commandHandler.HandleAssignRoleAsync(command);

            if (result.Success)
            {
                return Success(result, "Role assigned successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to assign role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error assigning role: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to assign role");
        }
    }

    /// <summary>
    /// 从管理员移除角色
    /// </summary>
    [HttpDelete("{adminId}/roles/{roleId}")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> RemoveRole(string adminId, string roleId)
    {
        try
        {
            var command = new RemoveRoleCommand
            {
                AdminId = adminId,
                RoleId = roleId,
                RemovedBy = "system" // TODO: 从Token中获取
            };

            var result = await _commandHandler.HandleRemoveRoleAsync(command);

            if (result.Success)
            {
                return Success(result, "Role removed successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to remove role", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error removing role: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to remove role");
        }
    }

    /// <summary>
    /// 启用两步验证
    /// </summary>
    [HttpPost("{adminId}/enable-2fa")]
    public async Task<ActionResult<ApiResponse<TwoFactorSetupResult>>> EnableTwoFactor(string adminId, [FromBody] EnableTwoFactorCommand command)
    {
        try
        {
            command.AdminId = adminId;

            var result = await _commandHandler.HandleEnableTwoFactorAsync(command);

            if (result.Success)
            {
                return Success(result, "Two-factor authentication enabled successfully");
            }

            return BusinessError<TwoFactorSetupResult>(result.Message ?? "Failed to enable two-factor authentication", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error enabling 2FA: {AdminId}", adminId);
            return InternalError<TwoFactorSetupResult>("Failed to enable two-factor authentication");
        }
    }

    /// <summary>
    /// 禁用两步验证
    /// </summary>
    [HttpPost("{adminId}/disable-2fa")]
    public async Task<ActionResult<ApiResponse<AdminOperationResult>>> DisableTwoFactor(string adminId, [FromBody] DisableTwoFactorCommand command)
    {
        try
        {
            command.AdminId = adminId;
            command.DisabledBy = "system"; // TODO: 从 Token 中获取

            var result = await _commandHandler.HandleDisableTwoFactorAsync(command);

            if (result.Success)
            {
                return Success(result, "Two-factor authentication disabled successfully");
            }

            return BusinessError<AdminOperationResult>(result.Message ?? "Failed to disable two-factor authentication", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error disabling 2FA: {AdminId}", adminId);
            return InternalError<AdminOperationResult>("Failed to disable two-factor authentication");
        }
    }

    /// <summary>
    /// 重新生成恢复代码
    /// </summary>
    [HttpPost("{adminId}/regenerate-recovery-codes")]
    public async Task<ActionResult<ApiResponse<RecoveryCodesResult>>> RegenerateRecoveryCodes(string adminId, [FromBody] RegenerateRecoveryCodesCommand command)
    {
        try
        {
            command.AdminId = adminId;

            var result = await _commandHandler.HandleRegenerateRecoveryCodesAsync(command);

            if (result.Success)
            {
                return Success(result, "Recovery codes regenerated successfully");
            }

            return BusinessError<RecoveryCodesResult>(result.Message ?? "Failed to regenerate recovery codes", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error regenerating recovery codes: {AdminId}", adminId);
            return InternalError<RecoveryCodesResult>("Failed to regenerate recovery codes");
        }
    }

    /// <summary>
    /// 获取剩余恢复代码数量
    /// </summary>
    [HttpGet("{adminId}/recovery-codes-count")]
    public async Task<ActionResult<ApiResponse<int>>> GetRemainingRecoveryCodesCount(string adminId)
    {
        try
        {
            var count = await _queryHandler.HandleGetRemainingRecoveryCodesCountAsync(adminId);
            return Success(count, "Recovery codes count retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting recovery codes count: {AdminId}", adminId);
            return InternalError<int>("Failed to get recovery codes count");
        }
    }

    /// <summary>
    /// 获取管理员权限
    /// </summary>
    [HttpGet("{adminId}/permissions")]
    public async Task<ActionResult<ApiResponse<List<PermissionDto>>>> GetAdminPermissions(string adminId)
    {
        try
        {
            var permissions = await _queryHandler.HandleGetPermissionsAsync(adminId);
            return Success(permissions, "Admin permissions retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin permissions: {AdminId}", adminId);
            return InternalError<List<PermissionDto>>("Failed to retrieve admin permissions");
        }
    }

    /// <summary>
    /// 获取管理员角色
    /// </summary>
    [HttpGet("{adminId}/roles")]
    public async Task<ActionResult<ApiResponse<List<RoleDto>>>> GetAdminRoles(string adminId)
    {
        try
        {
            var roles = await _queryHandler.HandleGetRolesAsync(adminId);
            return Success(roles, "Admin roles retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin roles: {AdminId}", adminId);
            return InternalError<List<RoleDto>>("Failed to retrieve admin roles");
        }
    }

    /// <summary>
    /// 检查管理员权限
    /// </summary>
    [HttpPost("{adminId}/check-permission")]
    public async Task<ActionResult<ApiResponse<PermissionCheckResult>>> CheckPermission(string adminId, [FromBody] CheckPermissionCommand command)
    {
        try
        {
            command.AdminId = adminId;

            var result = await _queryHandler.HandleHasPermissionAsync(command);
            return Success(result, "Permission check completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking permission: {AdminId}", adminId);
            return InternalError<PermissionCheckResult>("Failed to check permission");
        }
    }

    /// <summary>
    /// 搜索管理员
    /// </summary>
    [HttpPost("search")]
    public async Task<ActionResult<ApiResponse<PagedResult<AdminSummaryDto>>>> SearchAdmins([FromBody] AdminSearchRequest request)
    {
        try
        {
            var result = await _queryHandler.HandleSearchAdminsAsync(request);
            // 临时转换 - 需要创建AdminSummaryDto映射
            var summaryResult = new PagedResult<AdminSummaryDto>
            {
                Items = result.Items.Select(admin => new AdminSummaryDto
                {
                    Id = admin.Id,
                    Username = admin.Username,
                    Email = admin.Email,
                    Name = admin.Name,
                    Status = admin.Status,
                    CreatedAt = admin.CreatedAt,
                    LastLoginAt = admin.LastLoginAt
                }).ToList(),
                TotalCount = result.TotalCount
            };
            return Success(summaryResult, "Admin search completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error searching admins");
            return InternalError<PagedResult<AdminSummaryDto>>("Failed to search admins");
        }
    }

    /// <summary>
    /// 获取管理员统计信息
    /// </summary>
    [HttpGet("stats")]
    public async Task<ActionResult<ApiResponse<AdminStatsDto>>> GetAdminStats()
    {
        try
        {
            var stats = await _queryHandler.HandleGetAdminStatsAsync();
            return Success(stats, "Admin statistics retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting admin stats");
            return InternalError<AdminStatsDto>("Failed to retrieve admin statistics");
        }
    }
}

// 辅助请求模型
internal class ResetPasswordRequest
{
    public string NewPassword { get; set; } = string.Empty;
}
