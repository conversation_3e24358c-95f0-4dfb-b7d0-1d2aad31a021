using Microsoft.AspNetCore.Mvc;
using Curio.Commands.Handlers;
using Curio.Queries.Handlers;
using Curio.Shared.Enums;
using Curio.Api.Models;
using Curio.Api.Constants;
using Curio.Shared.Users;

namespace Curio.Api.Controllers;

/// <summary>
/// 用户控制器 - 使用简化的CQRS架构
/// 移除DDD复杂性，直接使用命令/查询处理器
/// </summary>
[ApiController]
[Route("api/[controller]")]
internal sealed class UsersController(
    UserCommandHandler commandHandler,
    UserQueryHandler queryHandler,
    ILogger<UsersController> logger) : BaseController
{
    private readonly UserCommandHandler _commandHandler = commandHandler;
    private readonly UserQueryHandler _queryHandler = queryHandler;
    private readonly ILogger<UsersController> _logger = logger;

    [HttpPost("check-email")]
    public async Task<ActionResult<ApiResponse<EmailExistsResult>>> CheckEmailExists([FromBody] CheckEmailExistsCommand command)
    {
        try
        {
            var result = await _queryHandler.HandleCheckEmailExistsAsync(command);
            return Success(result, "Email existence check completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking email existence for {Email}", command.Email);
            return InternalError<EmailExistsResult>("Failed to check email existence");
        }
    }

    [HttpPost("send-verification-code")]
    public async Task<ActionResult<ApiResponse<bool>>> SendVerificationCode([FromBody] SendVerificationCodeCommand command)
    {
        try
        {
            var result = await _commandHandler.HandleSendVerificationCodeAsync(command);
            return Success(result, "Verification code sent successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error sending verification code to {Email}", command.Email);
            return InternalError<bool>("Failed to send verification code");
        }
    }

    [HttpPost("register")]
    public async Task<ActionResult<ApiResponse<VerificationResult>>> Register([FromBody] RegisterUserCommand command)
    {
        try
        {
            var result = await _commandHandler.HandleRegisterUserAsync(command);

            if (result.Success)
            {
                return Created(result, "User registered successfully");
            }

            return BusinessError<VerificationResult>(result.Message ?? "Registration failed", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error registering user {Email}", command.Email);
            return InternalError<VerificationResult>("Failed to register user");
        }
    }

    [HttpPost("login")]
    public async Task<ActionResult<ApiResponse<VerificationResult>>> Login([FromBody] LoginUserCommand command)
    {
        try
        {
            var result = await _commandHandler.HandleLoginUserAsync(command);

            if (result.Success)
            {
                return Success(result, "User logged in successfully");
            }

            return BusinessError<VerificationResult>(result.Message ?? "Login failed", ApiCodes.BusinessRuleValidationFailed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error logging in user {Email}", command.Email);
            return InternalError<VerificationResult>("Failed to login user");
        }
    }

    [HttpGet("{email}")]
    public async Task<ActionResult<ApiResponse<UserDto>>> GetUser(string email)
    {
        try
        {
            var user = await _queryHandler.HandleGetUserAsync(email);

            if (user == null)
            {
                return NotFoundError<UserDto>($"User with email '{email}' not found");
            }

            return Success(user, "User retrieved successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user {Email}", email);
            return InternalError<UserDto>("Failed to retrieve user");
        }
    }
}
