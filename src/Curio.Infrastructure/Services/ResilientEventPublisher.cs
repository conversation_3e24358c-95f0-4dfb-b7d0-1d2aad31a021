using System.Text.Json;
using Orleans.Streams;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Curio.Infrastructure.Configuration;
using Curio.Shared.Enums;
using Curio.Shared.Events;

namespace Curio.Infrastructure.Services;

/// <summary>
/// 弹性事件发布器实现
/// 提供重试机制、死信队列和本地备份功能
/// </summary>
public class ResilientEventPublisher : IResilientEventPublisher
{
    private readonly IStreamProvider _streamProvider;
    private readonly ILogger<ResilientEventPublisher> _logger;
    private readonly ResilientPublishOptions _options;

    public ResilientEventPublisher(
        IStreamProvider streamProvider,
        ILogger<ResilientEventPublisher> logger,
        IOptions<ResilientPublishOptions> options)
    {
        _streamProvider = streamProvider;
        _logger = logger;
        _options = options.Value;
    }

    public async Task PublishAsync<T>(T @event, string streamId) where T : DomainEvent
    {
        // 确保StreamId安全，避免特殊字符导致分区键冲突
        var safeStreamId = streamId.Replace("@", "-").Replace(".", "-");
        var stream = _streamProvider.GetStream<T>("domain-events", safeStreamId);
        await PublishWithRetryAsync(stream, @event, safeStreamId);
    }

    public async Task PublishBatchAsync<T>(IEnumerable<T> events, string streamId) where T : DomainEvent
    {
        // 确保StreamId安全，避免特殊字符导致分区键冲突
        var safeStreamId = streamId.Replace("@", "-").Replace(".", "-");
        var stream = _streamProvider.GetStream<T>("domain-events", safeStreamId);

        foreach (var @event in events)
        {
            await PublishWithRetryAsync(stream, @event, safeStreamId);
        }
    }

    private async Task PublishWithRetryAsync<T>(IAsyncStream<T> stream, T @event, string streamId) where T : DomainEvent
    {
        var retryCount = 0;

        while (retryCount < _options.MaxRetries)
        {
            try
            {
                using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.PublishTimeoutSeconds));
                await stream.OnNextAsync(@event);

                _logger.LogDebug("Event published successfully: {EventId}, Type: {EventType}",
                    @event.EventId, typeof(T).Name);
                return; // 成功发布，退出重试循环
            }
            catch (OperationCanceledException)
            {
                retryCount++;
                _logger.LogWarning("Event publish timeout (attempt {Attempt}/{MaxRetries}): {EventId}",
                    retryCount, _options.MaxRetries, @event.EventId);
            }
            catch (Exception ex)
            {
                retryCount++;
                _logger.LogWarning(ex,
                    "Failed to publish event to stream (attempt {Attempt}/{MaxRetries}): {EventId}",
                    retryCount, _options.MaxRetries, @event.EventId);
            }

            if (retryCount >= _options.MaxRetries)
            {
                // 达到最大重试次数，进入失败处理流程
                await HandlePublishFailureAsync(@event, streamId, retryCount);
                break;
            }

            // 指数退避策略
            var delay = TimeSpan.FromMilliseconds(
                Math.Pow(2, retryCount) * _options.BaseDelayMs);

            _logger.LogDebug("Retrying event publish after {Delay}ms: {EventId}",
                delay.TotalMilliseconds, @event.EventId);

            await Task.Delay(delay);
        }
    }

    private async Task HandlePublishFailureAsync<T>(T @event, string streamId, int retryCount) where T : DomainEvent
    {
        var failureReason = $"Failed to publish after {retryCount} attempts";

        _logger.LogError("Event publish failed permanently: {EventId}, StreamId: {StreamId}, Reason: {Reason}",
            @event.EventId, streamId, failureReason);

        // 1. 发送到死信队列
        if (_options.EnableDeadLetterQueue)
        {
            await SendToDeadLetterQueueAsync(@event, streamId, failureReason);
        }

        // 2. 本地备份
        if (_options.EnableLocalBackup)
        {
            await WriteToLocalBackupAsync(@event, streamId, failureReason);
        }
    }

    private async Task SendToDeadLetterQueueAsync<T>(T @event, string streamId, string failureReason) where T : DomainEvent
    {
        try
        {
            var deadLetterEvent = new DeadLetterEvent
            {
                OriginalEventId = @event.EventId,
                OriginalEventType = typeof(T).Name,
                OriginalEventData = JsonSerializer.Serialize(@event),
                FailureReason = failureReason,
                FailureTimestamp = DateTime.UtcNow,
                RetryCount = _options.MaxRetries,
                GrainId = streamId
            };

            var deadLetterStream = _streamProvider
                .GetStream<DeadLetterEvent>("dead-letter-queue", streamId.Replace("@", "-").Replace(".", "-"));

            using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(_options.PublishTimeoutSeconds));
            await deadLetterStream.OnNextAsync(deadLetterEvent);

            _logger.LogError("Event sent to dead letter queue: {EventId}, Type: {EventType}, Reason: {Reason}",
                @event.EventId, typeof(T).Name, failureReason);
        }
        catch (Exception dlqEx)
        {
            // 死信队列也失败了，记录严重错误
            _logger.LogCritical(dlqEx,
                "CRITICAL: Failed to send event to dead letter queue: {EventId}, Type: {EventType}. Event may be lost!",
                @event.EventId, typeof(T).Name);
        }
    }

    private async Task WriteToLocalBackupAsync<T>(T @event, string streamId, string failureReason) where T : DomainEvent
    {
        try
        {
            var backupPath = Path.Combine(_options.BackupDirectory,
                $"{DateTime.UtcNow:yyyy-MM-dd}",
                $"{@event.EventId}_{typeof(T).Name}.json");

            Directory.CreateDirectory(Path.GetDirectoryName(backupPath)!);

            var backupData = new
            {
                Event = @event,
                EventType = typeof(T).Name,
                StreamId = streamId,
                FailureReason = failureReason,
                FailureTimestamp = DateTime.UtcNow,
                RetryCount = _options.MaxRetries
            };

            var json = JsonSerializer.Serialize(backupData, new JsonSerializerOptions
            {
                WriteIndented = true
            });

            await File.WriteAllTextAsync(backupPath, json);

            _logger.LogCritical("Event backed up to local file: {BackupPath}, EventId: {EventId}",
                backupPath, @event.EventId);
        }
        catch (Exception backupEx)
        {
            _logger.LogCritical(backupEx,
                "CRITICAL: Failed to backup event to local file: {EventId}, Type: {EventType}. Event may be permanently lost!",
                @event.EventId, typeof(T).Name);
        }
    }
}
