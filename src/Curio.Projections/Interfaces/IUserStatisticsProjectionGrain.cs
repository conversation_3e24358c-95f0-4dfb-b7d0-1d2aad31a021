using Orleans;
using Curio.Shared.Enums;
using Curio.Shared.Events;

namespace Curio.Projections.Interfaces;

/// <summary>
/// 用户统计投影接口 - 基于Orleans的实时投影
/// </summary>
public interface IUserStatisticsProjectionGrain : IGrainWithStringKey
{
    /// <summary>
    /// 处理用户相关事件，更新统计信息
    /// </summary>
    Task ProcessUserEventAsync(DomainEvent userEvent);

    /// <summary>
    /// 获取用户统计信息
    /// </summary>
    Task<UserStatistics> GetStatisticsAsync();

    /// <summary>
    /// 获取特定邮箱域名的统计
    /// </summary>
    Task<DomainStatistics> GetDomainStatisticsAsync(string domain);
}

/// <summary>
/// 用户统计数据
/// </summary>
[GenerateSerializer]
public class UserStatistics
{
    [Id(0)] public int TotalUsers { get; set; }
    [Id(1)] public int VerifiedUsers { get; set; }
    [Id(2)] public int PendingVerification { get; set; }
    [Id(3)] public Dictionary<string, int> UsersByDomain { get; set; } = new();
    [Id(4)] public DateTime LastUpdated { get; set; }
}

/// <summary>
/// 域名统计数据
/// </summary>
[GenerateSerializer]
public class DomainStatistics
{
    [Id(0)] public string Domain { get; set; } = string.Empty;
    [Id(1)] public int TotalUsers { get; set; }
    [Id(2)] public int VerifiedUsers { get; set; }
    [Id(3)] public DateTime LastUpdated { get; set; }
}
