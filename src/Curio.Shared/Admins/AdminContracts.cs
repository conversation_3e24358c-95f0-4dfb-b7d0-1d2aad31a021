using Orleans;
using Curio.Shared.Enums;

namespace Curio.Shared.Admins;

// === Commands ===

[GenerateSerializer]
[Alias("Curio.Shared.Admins.CreateAdminCommand")]
public class CreateAdminCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public string InitialPassword { get; set; } = string.Empty;
    [Id(5)] public ICollection<string> RoleIds { get; } = new List<string>();
    [Id(6)] public string CreatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.UpdateAdminCommand")]
public class UpdateAdminCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.ChangeAdminPasswordCommand")]
public class ChangeAdminPasswordCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string CurrentPassword { get; set; } = string.Empty;
    [Id(3)] public string NewPassword { get; set; } = string.Empty;
    [Id(4)] public bool IsInitialSetup { get; set; } = false;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.AdminLoginCommand")]
public class AdminLoginCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Password { get; set; } = string.Empty;
    [Id(3)] public string? TwoFactorCode { get; set; }
    [Id(4)] public string IpAddress { get; set; } = string.Empty;
    [Id(5)] public string UserAgent { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.EnableTwoFactorCommand")]
public class EnableTwoFactorCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string VerificationCode { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.VerifyTwoFactorCommand")]
public class VerifyTwoFactorCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string Code { get; set; } = string.Empty;
    [Id(3)] public string LoginSessionId { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.DisableTwoFactorCommand")]
public class DisableTwoFactorCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string CurrentPassword { get; set; } = string.Empty;
    [Id(3)] public string DisabledBy { get; set; } = string.Empty;
    [Id(4)] public string Reason { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.RegenerateRecoveryCodesCommand")]
public class RegenerateRecoveryCodesCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string CurrentPassword { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.UseRecoveryCodeCommand")]
public class UseRecoveryCodeCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string RecoveryCode { get; set; } = string.Empty;
    [Id(3)] public string IpAddress { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.AssignRoleCommand")]
public class AssignRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string RoleId { get; set; } = string.Empty;
    [Id(3)] public string AssignedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.RemoveRoleCommand")]
public class RemoveRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string RoleId { get; set; } = string.Empty;
    [Id(3)] public string RemovedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.UpdateAdminStatusCommand")]
public class UpdateAdminStatusCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public AdminStatus NewStatus { get; set; }
    [Id(3)] public string Reason { get; set; } = string.Empty;
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

// === Role Commands ===

[GenerateSerializer]
[Alias("Curio.Shared.Admins.CreateRoleCommand")]
public class CreateRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleName { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public ICollection<PermissionAssignment> Permissions { get; } = new List<PermissionAssignment>();
    [Id(4)] public string CreatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.UpdateRoleCommand")]
public class UpdateRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public string RoleName { get; set; } = string.Empty;
    [Id(3)] public string Description { get; set; } = string.Empty;
    [Id(4)] public string UpdatedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.DeleteRoleCommand")]
public class DeleteRoleCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public string DeletedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.AssignPermissionCommand")]
public class AssignPermissionCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public PermissionResource Resource { get; set; }
    [Id(3)] public PermissionAction Action { get; set; }
    [Id(4)] public string AssignedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.RemovePermissionCommand")]
public class RemovePermissionCommand
{
    [Id(0)] public string CommandId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public string RoleId { get; set; } = string.Empty;
    [Id(2)] public PermissionResource Resource { get; set; }
    [Id(3)] public PermissionAction Action { get; set; }
    [Id(4)] public string RemovedBy { get; set; } = string.Empty;
}

// === Query Commands ===

[GenerateSerializer]
public class CheckPermissionCommand
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public PermissionResource Resource { get; set; }
    [Id(2)] public PermissionAction Action { get; set; }
}

[GenerateSerializer]
public class GetAdminPermissionsCommand
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
}

// === Helper Classes ===

[GenerateSerializer]
public class PermissionAssignment
{
    [Id(0)] public PermissionResource Resource { get; set; }
    [Id(1)] public PermissionAction Action { get; set; }
}

// === Results ===

[GenerateSerializer]
public class AdminOperationResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public AdminDto? Admin { get; set; }
    [Id(3)] public string? ErrorCode { get; set; }
}

[GenerateSerializer]
public class LoginResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public AdminDto? Admin { get; set; }
    [Id(3)] public string? AccessToken { get; set; }
    [Id(4)] public string? RefreshToken { get; set; }
    [Id(5)] public bool RequiresTwoFactor { get; set; }
    [Id(6)] public string? TwoFactorSessionId { get; set; }
    [Id(7)] public DateTime? ExpiresAt { get; set; }
}

[GenerateSerializer]
public class TwoFactorSetupResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public string? SecretKey { get; set; }
    [Id(3)] public string? QrCodeUrl { get; set; }
    [Id(4)] public ICollection<string> RecoveryCodes { get; } = new List<string>();
}

[GenerateSerializer]
public class RecoveryCodesResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public ICollection<string> RecoveryCodes { get; } = new List<string>();
    [Id(3)] public int RemainingCodes { get; set; }
}

[GenerateSerializer]
public class RoleOperationResult
{
    [Id(0)] public bool Success { get; set; }
    [Id(1)] public string Message { get; set; } = string.Empty;
    [Id(2)] public RoleDto? Role { get; set; }
    [Id(3)] public string? ErrorCode { get; set; }
}

[GenerateSerializer]
public class PermissionCheckResult
{
    [Id(0)] public bool HasPermission { get; set; }
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public PermissionResource Resource { get; set; }
    [Id(3)] public PermissionAction Action { get; set; }
    [Id(4)] public ICollection<string> GrantingRoles { get; } = new List<string>();
}
