using Orleans;
using Curio.Shared.Enums;

namespace Curio.Shared.Admins;

// === Admin DTOs ===

[GenerateSerializer]
public class AdminDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public AdminStatus Status { get; set; }
    [Id(5)] public DateTime CreatedAt { get; set; }
    [Id(6)] public DateTime? LastLoginAt { get; set; }
    [Id(7)] public DateTime? UpdatedAt { get; set; }
    [Id(8)] public string CreatedBy { get; set; } = string.Empty;
    [Id(9)] public ICollection<string> RoleIds { get; } = new List<string>();
    [Id(10)] public ICollection<RoleDto> Roles { get; } = new List<RoleDto>();
    [Id(11)] public bool TwoFactorEnabled { get; set; }
    [Id(12)] public DateTime? TwoFactorEnabledAt { get; set; }
    [Id(13)] public int FailedLoginAttempts { get; set; }
    [Id(14)] public bool IsLocked { get; set; }
    [Id(15)] public bool MustChangePassword { get; set; }
    [Id(16)] public string? LastLoginIp { get; set; }
    [Id(17)] public ICollection<PermissionDto> EffectivePermissions { get; } = new List<PermissionDto>();
}

[GenerateSerializer]
public class AdminSummaryDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Username { get; set; } = string.Empty;
    [Id(2)] public string Email { get; set; } = string.Empty;
    [Id(3)] public string Name { get; set; } = string.Empty;
    [Id(4)] public AdminStatus Status { get; set; }
    [Id(5)] public DateTime CreatedAt { get; set; }
    [Id(6)] public DateTime? LastLoginAt { get; set; }
    [Id(7)] public bool TwoFactorEnabled { get; set; }
    [Id(8)] public int RoleCount { get; set; }
    [Id(9)] public bool IsLocked { get; set; }
}

// === Role DTOs ===

[GenerateSerializer]
public class RoleDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Name { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public DateTime CreatedAt { get; set; }
    [Id(4)] public DateTime? UpdatedAt { get; set; }
    [Id(5)] public string CreatedBy { get; set; } = string.Empty;
    [Id(6)] public bool IsActive { get; set; }
    [Id(7)] public ICollection<PermissionDto> Permissions { get; } = new List<PermissionDto>();
    [Id(8)] public int PermissionCount { get; set; }
    [Id(9)] public bool IsBuiltIn { get; set; }
    [Id(10)] public int AssignedAdminCount { get; set; }
}

[GenerateSerializer]
public class RoleSummaryDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string Name { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public bool IsActive { get; set; }
    [Id(4)] public int PermissionCount { get; set; }
    [Id(5)] public int AssignedAdminCount { get; set; }
    [Id(6)] public bool IsBuiltIn { get; set; }
}

// === Permission DTOs ===

[GenerateSerializer]
public class PermissionDto
{
    [Id(0)] public PermissionResource Resource { get; set; }
    [Id(1)] public PermissionAction Action { get; set; }
    [Id(2)] public string ResourceName { get; set; } = string.Empty;
    [Id(3)] public string ActionName { get; set; } = string.Empty;
    [Id(4)] public string Description { get; set; } = string.Empty;
    [Id(5)] public DateTime AssignedAt { get; set; }
    [Id(6)] public string AssignedBy { get; set; } = string.Empty;
    [Id(7)] public string? GrantedByRole { get; set; }
}

[GenerateSerializer]
public class PermissionGroupDto
{
    [Id(0)] public PermissionResource Resource { get; set; }
    [Id(1)] public string ResourceName { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public ICollection<PermissionActionDto> Actions { get; } = new List<PermissionActionDto>();
}

[GenerateSerializer]
public class PermissionActionDto
{
    [Id(0)] public PermissionAction Action { get; set; }
    [Id(1)] public string ActionName { get; set; } = string.Empty;
    [Id(2)] public string Description { get; set; } = string.Empty;
    [Id(3)] public bool IsGranted { get; set; }
}

// === Audit DTOs ===

[GenerateSerializer]
public class AuditLogDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string AdminId { get; set; } = string.Empty;
    [Id(2)] public string AdminUsername { get; set; } = string.Empty;
    [Id(3)] public AuditActionType ActionType { get; set; }
    [Id(4)] public string ActionDescription { get; set; } = string.Empty;
    [Id(5)] public PermissionResource? Resource { get; set; }
    [Id(6)] public string? ResourceId { get; set; }
    [Id(7)] public AuditLevel Level { get; set; }
    [Id(8)] public DateTime Timestamp { get; set; }
    [Id(9)] public string IpAddress { get; set; } = string.Empty;
    [Id(10)] public string UserAgent { get; set; } = string.Empty;
    [Id(11)] public string? Details { get; set; }
    [Id(12)] public bool Success { get; set; }
    [Id(13)] public string? ErrorMessage { get; set; }
}

[GenerateSerializer]
public class AuditLogSummaryDto
{
    [Id(0)] public string Id { get; set; } = string.Empty;
    [Id(1)] public string AdminUsername { get; set; } = string.Empty;
    [Id(2)] public AuditActionType ActionType { get; set; }
    [Id(3)] public string ActionDescription { get; set; } = string.Empty;
    [Id(4)] public AuditLevel Level { get; set; }
    [Id(5)] public DateTime Timestamp { get; set; }
    [Id(6)] public bool Success { get; set; }
}

// === Statistics DTOs ===

[GenerateSerializer]
public class AdminStatsDto
{
    [Id(0)] public int TotalAdmins { get; set; }
    [Id(1)] public int ActiveAdmins { get; set; }
    [Id(2)] public int LockedAdmins { get; set; }
    [Id(3)] public int AdminsWithTwoFactor { get; set; }
    [Id(4)] public int PendingSetupAdmins { get; set; }
    [Id(5)] public DateTime LastUpdated { get; set; }
}

[GenerateSerializer]
public class RoleStatsDto
{
    [Id(0)] public int TotalRoles { get; set; }
    [Id(1)] public int ActiveRoles { get; set; }
    [Id(2)] public int BuiltInRoles { get; set; }
    [Id(3)] public int CustomRoles { get; set; }
    [Id(4)] public DateTime LastUpdated { get; set; }
}

[GenerateSerializer]
public class SecurityStatsDto
{
    [Id(0)] public int TotalLoginAttempts { get; set; }
    [Id(1)] public int SuccessfulLogins { get; set; }
    [Id(2)] public int FailedLogins { get; set; }
    [Id(3)] public int LockedAccounts { get; set; }
    [Id(4)] public int TwoFactorUsers { get; set; }
    [Id(5)] public DateTime FromDate { get; set; }
    [Id(6)] public DateTime ToDate { get; set; }
}

// === Search and Filter DTOs ===

[GenerateSerializer]
public class AdminSearchRequest
{
    [Id(0)] public string? Keyword { get; set; }
    [Id(1)] public AdminStatus? Status { get; set; }
    [Id(2)] public ICollection<string> RoleIds { get; } = new List<string>();
    [Id(3)] public bool? TwoFactorEnabled { get; set; }
    [Id(4)] public DateTime? CreatedAfter { get; set; }
    [Id(5)] public DateTime? CreatedBefore { get; set; }
    [Id(6)] public int Skip { get; set; } = 0;
    [Id(7)] public int Take { get; set; } = 20;
    [Id(8)] public string SortBy { get; set; } = "CreatedAt";
    [Id(9)] public bool SortDescending { get; set; } = true;
}

[GenerateSerializer]
public class AuditLogSearchRequest
{
    [Id(0)] public string? AdminId { get; set; }
    [Id(1)] public AuditActionType? ActionType { get; set; }
    [Id(2)] public PermissionResource? Resource { get; set; }
    [Id(3)] public AuditLevel? Level { get; set; }
    [Id(4)] public DateTime? FromDate { get; set; }
    [Id(5)] public DateTime? ToDate { get; set; }
    [Id(6)] public bool? Success { get; set; }
    [Id(7)] public string? IpAddress { get; set; }
    [Id(8)] public int Skip { get; set; } = 0;
    [Id(9)] public int Take { get; set; } = 50;
    [Id(10)] public string SortBy { get; set; } = "Timestamp";
    [Id(11)] public bool SortDescending { get; set; } = true;
}

[GenerateSerializer]
[Alias("Curio.Shared.Admins.PagedResult`1")]
public class PagedResult<T>
{
    [Id(0)] public ICollection<T> Items { get; set; } = [];
    [Id(1)] public int TotalCount { get; set; }
    [Id(2)] public int PageSize { get; set; }
    [Id(3)] public int Page { get; set; }
    [Id(4)] public int TotalPages { get; set; }
    [Id(5)] public bool HasNext { get; set; }
    [Id(6)] public bool HasPrevious { get; set; }
}

// === 权限继承 DTOs ===

[GenerateSerializer]
public class PermissionInheritanceDto
{
    [Id(0)] public PermissionResource Resource { get; set; }
    [Id(1)] public PermissionAction Action { get; set; }
    [Id(2)] public string ResourceName { get; set; } = string.Empty;
    [Id(3)] public string ActionName { get; set; } = string.Empty;
    [Id(4)] public ICollection<PermissionSourceDto> Sources { get; } = new List<PermissionSourceDto>();
    [Id(5)] public bool IsGranted { get; set; }
    [Id(6)] public DateTime? GrantedAt { get; set; }
}

[GenerateSerializer]
public class PermissionSourceDto
{
    [Id(0)] public string SourceType { get; set; } = string.Empty; // "Role" or "Direct"
    [Id(1)] public string SourceId { get; set; } = string.Empty;
    [Id(2)] public string SourceName { get; set; } = string.Empty;
    [Id(3)] public DateTime GrantedAt { get; set; }
    [Id(4)] public string GrantedBy { get; set; } = string.Empty;
}

[GenerateSerializer]
public class PermissionCacheDto
{
    [Id(0)] public string AdminId { get; set; } = string.Empty;
    [Id(1)] public Dictionary<string, bool> Permissions { get; } = [];
    [Id(2)] public ICollection<string> RoleIds { get; } = [];
    [Id(3)] public DateTime LastUpdated { get; set; }
    [Id(4)] public DateTime ExpiresAt { get; set; }
}
