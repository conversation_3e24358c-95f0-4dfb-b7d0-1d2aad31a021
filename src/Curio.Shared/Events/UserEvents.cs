using Orleans;

namespace Curio.Shared.Events;

// 基础领域事件
[GenerateSerializer]
public abstract class DomainEvent
{
    [Id(0)] public string EventId { get; set; } = Guid.NewGuid().ToString();
    [Id(1)] public DateTime Timestamp { get; set; } = DateTime.UtcNow;
    [Id(2)] public string CommandId { get; set; } = string.Empty;
    [Id(3)] public string IdempotencyKey { get; set; } = string.Empty;
}

// 用户相关领域事件
[GenerateSerializer]
public class UserRegistrationInitiatedEvent : DomainEvent
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string VerificationCode { get; set; } = string.Empty;
    [Id(2)] public DateTime ExpiresAt { get; set; }
}

[GenerateSerializer]
public class UserRegisteredEvent : DomainEvent
{
    [Id(0)] public string UserId { get; set; } = string.Empty;
    [Id(1)] public string Email { get; set; } = string.Empty;
    [Id(2)] public string Name { get; set; } = string.Empty;
    [Id(3)] public DateTime RegisteredAt { get; set; }
    [Id(4)] public string? RequestSource { get; set; } // 请求来源（web-api, legacy-api, etc.）
}

[GenerateSerializer]
public class UserLoginAttemptedEvent : DomainEvent
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public bool Success { get; set; }
    [Id(2)] public string? FailureReason { get; set; }
    [Id(3)] public DateTime AttemptedAt { get; set; }
}

[GenerateSerializer]
public class VerificationCodeGeneratedEvent : DomainEvent
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Code { get; set; } = string.Empty;
    [Id(2)] public DateTime ExpiresAt { get; set; }
    [Id(3)] public string Purpose { get; set; } = string.Empty; // "registration" or "login"
}

// Email sending related events
[GenerateSerializer]
public class EmailSendingInitiatedEvent : DomainEvent
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Purpose { get; set; } = string.Empty;
    [Id(2)] public string VerificationEventId { get; set; } = string.Empty;
    [Id(3)] public DateTime InitiatedAt { get; set; }
}

[GenerateSerializer]
public class EmailSentSuccessfullyEvent : DomainEvent
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Purpose { get; set; } = string.Empty;
    [Id(2)] public string VerificationEventId { get; set; } = string.Empty;
    [Id(3)] public DateTime SentAt { get; set; }
    [Id(4)] public int AttemptCount { get; set; }
}

[GenerateSerializer]
public class EmailSendingFailedEvent : DomainEvent
{
    [Id(0)] public string Email { get; set; } = string.Empty;
    [Id(1)] public string Purpose { get; set; } = string.Empty;
    [Id(2)] public string VerificationEventId { get; set; } = string.Empty;
    [Id(3)] public string ErrorMessage { get; set; } = string.Empty;
    [Id(4)] public DateTime FailedAt { get; set; }
    [Id(5)] public int AttemptCount { get; set; }
    [Id(6)] public bool WillRetry { get; set; }
}

// 死信队列事件
[GenerateSerializer]
public class DeadLetterEvent : DomainEvent
{
    [Id(0)] public string OriginalEventId { get; set; } = string.Empty;
    [Id(1)] public string OriginalEventType { get; set; } = string.Empty;
    [Id(2)] public string OriginalEventData { get; set; } = string.Empty;
    [Id(3)] public string FailureReason { get; set; } = string.Empty;
    [Id(4)] public DateTime FailureTimestamp { get; set; }
    [Id(5)] public int RetryCount { get; set; }
    [Id(6)] public string GrainId { get; set; } = string.Empty;
}
