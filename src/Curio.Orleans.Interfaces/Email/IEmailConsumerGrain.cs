using Orleans;
using Curio.Shared.Enums;
using Curio.Shared.Events;

namespace Curio.Orleans.Interfaces.Email;

/// <summary>
/// Orleans Grain interface for consuming email-related events and handling asynchronous email delivery
/// </summary>
public interface IEmailConsumerGrain : IGrainWithStringKey
{
    /// <summary>
    /// Process verification code generated event and send email asynchronously
    /// </summary>
    /// <param name="event">The verification code generated event</param>
    /// <returns>Task representing the async operation</returns>
    Task ProcessVerificationCodeEventAsync(VerificationCodeGeneratedEvent @event);

    /// <summary>
    /// Get email sending status for a specific event
    /// </summary>
    /// <param name="eventId">The event ID to check status for</param>
    /// <returns>Email sending result information</returns>
    Task<EmailSendingResult?> GetEmailSendingStatusAsync(string eventId);

    /// <summary>
    /// Get email sending statistics for this consumer grain
    /// </summary>
    /// <returns>Email sending statistics</returns>
    Task<EmailSendingStats> GetSendingStatsAsync();
}

/// <summary>
/// Represents the result of an email sending operation
/// </summary>
[GenerateSerializer]
public class EmailSendingResult
{
    [Id(0)] public string EventId { get; set; } = string.Empty;
    [Id(1)] public string Email { get; set; } = string.Empty;
    [Id(2)] public string Purpose { get; set; } = string.Empty;
    [Id(3)] public EmailSendingStatus Status { get; set; }
    [Id(4)] public string? ErrorMessage { get; set; }
    [Id(5)] public DateTime ProcessedAt { get; set; }
    [Id(6)] public int AttemptCount { get; set; }
}

/// <summary>
/// Email sending status enumeration
/// </summary>
public enum EmailSendingStatus
{
    Pending,
    Processing,
    Sent,
    Failed,
    Retrying
}

/// <summary>
/// Statistics for email sending operations
/// </summary>
[GenerateSerializer]
public class EmailSendingStats
{
    [Id(0)] public int TotalProcessed { get; set; }
    [Id(1)] public int SuccessfulSent { get; set; }
    [Id(2)] public int Failed { get; set; }
    [Id(3)] public int Pending { get; set; }
    [Id(4)] public DateTime LastProcessedAt { get; set; }
}
