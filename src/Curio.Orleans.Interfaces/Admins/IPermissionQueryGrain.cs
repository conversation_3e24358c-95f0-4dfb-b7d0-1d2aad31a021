using Orleans;
using Curio.Shared.Admins;
using Curio.Shared.Enums;

namespace Curio.Orleans.Interfaces.Admins;

/// <summary>
/// 权限查询Grain接口
/// 用于高效查询管理员权限和角色信息
/// </summary>
public interface IPermissionQueryGrain : IGrainWithStringKey
{
    /// <summary>
    /// 检查管理员是否具有指定权限
    /// </summary>
    Task<bool> HasPermissionAsync(string adminId, PermissionResource resource, PermissionAction action);

    /// <summary>
    /// 获取管理员的所有权限
    /// </summary>
    Task<List<PermissionDto>> GetAdminPermissionsAsync(string adminId);

    /// <summary>
    /// 获取管理员的所有角色
    /// </summary>
    Task<List<RoleDto>> GetAdminRolesAsync(string adminId);

    /// <summary>
    /// 获取角色的所有权限
    /// </summary>
    Task<List<PermissionDto>> GetRolePermissionsAsync(string roleId);

    /// <summary>
    /// 检查角色是否具有指定权限
    /// </summary>
    Task<bool> RoleHasPermissionAsync(string roleId, PermissionResource resource, PermissionAction action);

    /// <summary>
    /// 获取具有指定权限的所有角色
    /// </summary>
    Task<List<RoleDto>> GetRolesWithPermissionAsync(PermissionResource resource, PermissionAction action);

    /// <summary>
    /// 获取分配了指定角色的所有管理员
    /// </summary>
    Task<List<AdminSummaryDto>> GetAdminsWithRoleAsync(string roleId);

    /// <summary>
    /// 刷新权限缓存
    /// </summary>
    Task RefreshPermissionCacheAsync();

    /// <summary>
    /// 批量检查权限
    /// </summary>
    Task<Dictionary<string, bool>> BatchCheckPermissionsAsync(string adminId, List<(PermissionResource resource, PermissionAction action)> permissions);

    /// <summary>
    /// 获取权限继承树
    /// </summary>
    Task<List<PermissionInheritanceDto>> GetPermissionInheritanceAsync(string adminId);
}
