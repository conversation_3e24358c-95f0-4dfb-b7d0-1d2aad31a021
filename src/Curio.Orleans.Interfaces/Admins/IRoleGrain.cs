using Orleans;
using Curio.Shared.Admins;
using Curio.Shared.Enums;

namespace Curio.Orleans.Interfaces.Admins;

public interface IRoleGrain : IGrainWithStringKey
{
    /// <summary>
    /// 获取角色信息
    /// </summary>
    Task<RoleDto?> GetRoleAsync();

    /// <summary>
    /// 创建角色
    /// </summary>
    Task<RoleOperationResult> CreateRoleAsync(CreateRoleCommand command);

    /// <summary>
    /// 更新角色信息
    /// </summary>
    Task<RoleOperationResult> UpdateRoleAsync(UpdateRoleCommand command);

    /// <summary>
    /// 删除角色
    /// </summary>
    Task<RoleOperationResult> DeleteRoleAsync(DeleteRoleCommand command);

    /// <summary>
    /// 分配权限到角色
    /// </summary>
    Task<RoleOperationResult> AssignPermissionAsync(AssignPermissionCommand command);

    /// <summary>
    /// 从角色移除权限
    /// </summary>
    Task<RoleOperationResult> RemovePermissionAsync(RemovePermissionCommand command);

    /// <summary>
    /// 获取角色的所有权限
    /// </summary>
    Task<List<PermissionDto>> GetPermissionsAsync();

    /// <summary>
    /// 检查角色是否有指定权限
    /// </summary>
    Task<bool> HasPermissionAsync(PermissionResource resource, PermissionAction action);

    /// <summary>
    /// 批量分配权限
    /// </summary>
    Task<RoleOperationResult> AssignPermissionsAsync(List<PermissionAssignment> permissions, string assignedBy);

    /// <summary>
    /// 批量移除权限
    /// </summary>
    Task<RoleOperationResult> RemovePermissionsAsync(List<PermissionAssignment> permissions, string removedBy);

    /// <summary>
    /// 获取分配了此角色的管理员数量
    /// </summary>
    Task<int> GetAssignedAdminCountAsync();
}
